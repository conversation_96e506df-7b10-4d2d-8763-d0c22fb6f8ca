import { Metadata } from 'next';
import ExpertiseContent from './ExpertiseContent';

export const metadata: Metadata = {
  title: 'Our Expertise | Metamorphic Labs',
  description: 'Discover our expertise in AI Platforms, Intelligent Automation, and Custom Software Development. We deliver cutting-edge solutions that transform businesses.',
  openGraph: {
    title: 'Our Expertise - Metamorphic Labs',
    description: 'AI Platforms, Intelligent Automation, and Custom Software Development expertise.',
    type: 'website',
  },
};

const expertiseData = {
  "ai-platforms": {
    title: "AI Platforms",
    subtitle: "Multi-Model AI Integration & Platform Development",
    description: "We specialize in building comprehensive AI platforms that integrate multiple models, APIs, and services into unified, scalable solutions.",
    features: [
      {
        title: "Multi-Model Integration",
        description: "Seamlessly integrate GPT-4, Claude, Gemini, and custom models",
        icon: "🔗",
        technologies: ["OpenAI API", "Anthropic Claude", "Google Gemini", "Custom Models"]
      },
      {
        title: "Intelligent Routing",
        description: "Smart query routing based on context, cost, and performance",
        icon: "🧠",
        technologies: ["ML Routing", "Cost Optimization", "Performance Analytics", "Fallback Systems"]
      },
      {
        title: "Platform Architecture",
        description: "Scalable, secure, and enterprise-ready AI infrastructure",
        icon: "🏗️",
        technologies: ["Kubernetes", "Docker", "Microservices", "API Gateway"]
      },
      {
        title: "Model Management",
        description: "Version control, deployment, and monitoring for AI models",
        icon: "⚙️",
        technologies: ["MLOps", "Model Versioning", "A/B Testing", "Performance Monitoring"]
      }
    ],
    caseStudy: {
      title: "Metamorphic AI Platform",
      description: "Our flagship platform that enables organizations to leverage multiple AI models through a single, unified interface.",
      results: ["50% reduction in AI integration time", "30% cost savings through intelligent routing", "99.9% uptime with automated failover"]
    }
  },
  "intelligent-automation": {
    title: "Intelligent Automation",
    subtitle: "AI-Driven Process Optimization & Workflow Automation",
    description: "Transform your business operations with intelligent automation that learns, adapts, and optimizes workflows in real-time.",
    features: [
      {
        title: "Process Intelligence",
        description: "AI-powered analysis and optimization of business processes",
        icon: "📊",
        technologies: ["Process Mining", "Workflow Analysis", "Bottleneck Detection", "Performance Metrics"]
      },
      {
        title: "Robotic Process Automation",
        description: "Intelligent bots that handle repetitive tasks with human-like decision making",
        icon: "🤖",
        technologies: ["UiPath", "Automation Anywhere", "Blue Prism", "Custom RPA Solutions"]
      },
      {
        title: "Document Processing",
        description: "Automated extraction and processing of structured and unstructured data",
        icon: "📄",
        technologies: ["OCR", "NLP", "Document AI", "Data Extraction APIs"]
      },
      {
        title: "Workflow Orchestration",
        description: "End-to-end automation of complex business workflows",
        icon: "🔄",
        technologies: ["Apache Airflow", "Zapier", "Microsoft Power Automate", "Custom Orchestration"]
      }
    ],
    caseStudy: {
      title: "Living Pipeline CI/CD",
      description: "AI-optimized CI/CD system that automatically detects issues, optimizes builds, and selects deployment strategies.",
      results: ["60% reduction in build times", "80% decrease in flaky tests", "50% cost savings through optimization"]
    }
  },
  "custom-software": {
    title: "Custom Software Development",
    subtitle: "Tailored Solutions for Complex Business Challenges",
    description: "We build custom software solutions that perfectly align with your business needs, from web applications to enterprise systems.",
    features: [
      {
        title: "Full-Stack Development",
        description: "End-to-end development from frontend to backend and database",
        icon: "💻",
        technologies: ["React", "Next.js", "Node.js", "Python", "PostgreSQL", "MongoDB"]
      },
      {
        title: "Cloud-Native Architecture",
        description: "Scalable, resilient applications built for the cloud",
        icon: "☁️",
        technologies: ["AWS", "Azure", "GCP", "Serverless", "Containers", "Microservices"]
      },
      {
        title: "API Development",
        description: "RESTful and GraphQL APIs with comprehensive documentation",
        icon: "🔌",
        technologies: ["REST APIs", "GraphQL", "OpenAPI", "API Gateway", "Rate Limiting", "Authentication"]
      },
      {
        title: "DevOps & CI/CD",
        description: "Automated deployment pipelines and infrastructure management",
        icon: "🚀",
        technologies: ["GitHub Actions", "Jenkins", "Terraform", "Docker", "Kubernetes", "Monitoring"]
      }
    ],
    caseStudy: {
      title: "Metamorphic SaaS Suite",
      description: "AI-powered platform that generates production-ready SaaS applications from natural language descriptions.",
      results: ["90% reduction in development time", "$100K+ savings per project", "Enterprise-grade security from day one"]
    }
  }
};

export default function ExpertisePage() {
  return <ExpertiseContent expertiseData={expertiseData} />;
}