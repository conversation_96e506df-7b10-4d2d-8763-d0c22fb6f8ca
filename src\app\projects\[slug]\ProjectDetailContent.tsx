'use client';

import { motion } from 'framer-motion';
import { MDXRemote } from 'next-mdx-remote/rsc';
import Image from 'next/image';
import Link from 'next/link';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ArrowLeft, Calendar, Tag, ExternalLink } from 'lucide-react';

interface Project {
  slug: string;
  title: string;
  summary: string;
  status: string;
  hero_url: string;
  tags: string[];
  body_md: string;
  created_at: string;
}

interface RelatedProject {
  slug: string;
  title: string;
  summary: string;
  status: string;
  hero_url: string;
  tags: string[];
}

interface ProjectDetailContentProps {
  project: Project;
  relatedProjects: RelatedProject[];
}

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

// Custom MDX components for better styling
const mdxComponents = {
  h1: ({ children }: { children: React.ReactNode }) => (
    <h2 className="text-3xl font-bold mb-6 text-primary">{children}</h2>
  ),
  h2: ({ children }: { children: React.ReactNode }) => (
    <h2 className="text-3xl font-bold mb-4 mt-8 text-primary border-b border-border pb-2">{children}</h2>
  ),
  h3: ({ children }: { children: React.ReactNode }) => (
    <h3 className="text-2xl font-semibold mb-3 mt-6 text-foreground">{children}</h3>
  ),
  p: ({ children }: { children: React.ReactNode }) => (
    <p className="text-muted-foreground leading-relaxed mb-4">{children}</p>
  ),
  ul: ({ children }: { children: React.ReactNode }) => (
    <ul className="list-disc list-inside space-y-2 mb-4 text-muted-foreground">{children}</ul>
  ),
  li: ({ children }: { children: React.ReactNode }) => (
    <li className="leading-relaxed">{children}</li>
  ),
  strong: ({ children }: { children: React.ReactNode }) => (
    <strong className="font-semibold text-foreground">{children}</strong>
  ),
  code: ({ children }: { children: React.ReactNode }) => (
    <code className="bg-muted px-2 py-1 rounded text-sm font-mono">{children}</code>
  ),
};

export default function ProjectDetailContent({ project, relatedProjects }: ProjectDetailContentProps) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const getStatusColor = (status: string) => {
    if (status.toLowerCase().includes('complete') || status.toLowerCase().includes('beta')) {
      return 'bg-green-100 text-green-800 border-green-200';
    }
    if (status.toLowerCase().includes('progress') || status.toLowerCase().includes('phase')) {
      return 'bg-blue-100 text-blue-800 border-blue-200';
    }
    return 'bg-yellow-100 text-yellow-800 border-yellow-200';
  };

  return (
    <motion.main
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="min-h-screen py-8 px-6 lg:px-24"
    >
      <div className="max-w-6xl mx-auto">
        {/* Navigation */}
        <motion.div
          variants={itemVariants}
          className="mb-8"
        >
          <Link href="/projects">
            <Button variant="ghost" className="mb-4">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Projects
            </Button>
          </Link>
        </motion.div>

        {/* Hero Section */}
        <motion.div
          variants={itemVariants}
          className="mb-12"
        >
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <div className="flex items-center gap-3 mb-4">
                <Badge className={`${getStatusColor(project.status)} border`}>
                  {project.status}
                </Badge>
                <div className="flex items-center text-sm text-muted-foreground">
                  <Calendar className="w-4 h-4 mr-1" />
                  {formatDate(project.created_at)}
                </div>
              </div>
              
              <h1 className="text-4xl lg:text-5xl font-bold mb-6 bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
                {project.title}
              </h1>
              
              <p className="text-xl text-muted-foreground leading-relaxed mb-6">
                {project.summary}
              </p>
              
              <div className="flex flex-wrap gap-2 mb-6">
                {project.tags.map((tag, index) => (
                  <Badge key={index} variant="secondary" className="flex items-center gap-1">
                    <Tag className="w-3 h-3" />
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
            
            <div className="relative">
              <div className="aspect-video rounded-xl overflow-hidden shadow-2xl border border-border">
                <Image
                  src={project.hero_url}
                  alt={project.title}
                  fill
                  className="object-cover"
                  priority
                />
              </div>
            </div>
          </div>
        </motion.div>

        <div className="grid lg:grid-cols-3 gap-12">
          {/* Main Content */}
          <motion.div
            variants={itemVariants}
            className="lg:col-span-2"
          >
            <Card className="shadow-lg border-0 bg-card/50 backdrop-blur">
              <CardContent className="p-8">
                {project.body_md ? (
                  <div className="prose prose-lg max-w-none">
                    <MDXRemote 
                      source={project.body_md} 
                      components={mdxComponents}
                    />
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <p className="text-muted-foreground mb-4">
                      Detailed content for this project is coming soon.
                    </p>
                    <p className="text-sm text-muted-foreground">
                      Check back later for comprehensive project details, technical specifications, and case studies.
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </motion.div>

          {/* Sidebar */}
          <motion.div
            variants={itemVariants}
            className="space-y-6"
          >
            {/* Project Info */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Project Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-semibold mb-2">Status</h4>
                  <Badge className={`${getStatusColor(project.status)} border`}>
                    {project.status}
                  </Badge>
                </div>
                
                <div>
                  <h4 className="font-semibold mb-2">Technologies</h4>
                  <div className="flex flex-wrap gap-1">
                    {project.tags.map((tag, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
                
                <div>
                  <h4 className="font-semibold mb-2">Created</h4>
                  <p className="text-sm text-muted-foreground">
                    {formatDate(project.created_at)}
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* CTA */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Interested in This Project?</CardTitle>
                <CardDescription>
                  Let&apos;s discuss how we can help you with similar solutions.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Link href="/contact">
                  <Button className="w-full">
                    <ExternalLink className="w-4 h-4 mr-2" />
                    Get In Touch
                  </Button>
                </Link>
              </CardContent>
            </Card>

            {/* Related Projects */}
            {relatedProjects.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Related Projects</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {relatedProjects.map((relatedProject) => (
                    <Link
                      key={relatedProject.slug}
                      href={`/projects/${relatedProject.slug}`}
                      className="block group"
                    >
                      <div className="border border-border rounded-lg p-3 hover:bg-muted/50 transition-colors">
                        <h4 className="font-semibold text-sm group-hover:text-primary transition-colors">
                          {relatedProject.title}
                        </h4>
                        <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                          {relatedProject.summary}
                        </p>
                        <Badge 
                          className={`${getStatusColor(relatedProject.status)} border text-xs mt-2`}
                        >
                          {relatedProject.status}
                        </Badge>
                      </div>
                    </Link>
                  ))}
                </CardContent>
              </Card>
            )}
          </motion.div>
        </div>
      </div>

      {/* JSON-LD Schema */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "SoftwareApplication",
            "name": project.title,
            "description": project.summary,
            "url": `https://metamorphiclabs.com/projects/${project.slug}`,
            "applicationCategory": "DeveloperApplication",
            "operatingSystem": "Any",
            "offers": {
              "@type": "Offer",
              "price": "0",
              "priceCurrency": "USD"
            },
            "creator": {
              "@type": "Organization",
              "name": "Metamorphic Labs"
            }
          })
        }}
      />
    </motion.main>
  );
}
