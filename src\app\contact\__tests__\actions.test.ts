import { test, expect, vi } from 'vitest';
import { submitContactForm } from '../actions';

// Mock the mailgun-js module
vi.mock('mailgun-js', () => {
  const send = vi.fn((data, callback) => {
    if (data.to === '<EMAIL>') {
      callback(null, { message: 'Queued. Thank you.' });
    } else {
      callback(new Error('Failed to send email'), null);
    }
  });
  return {
    __esModule: true,
    default: vi.fn(() => ({
      messages: {
        send: send,
      },
    })),
  };
});

test('submitContactForm sends email successfully', async () => {
  const formData = new FormData();
  formData.append('name', 'Test User');
  formData.append('email', '<EMAIL>');
  formData.append('message', 'This is a test message.');

  const result = await submitContactForm(null, formData);

  expect(result).toEqual({ success: true, message: 'Message received!' });
});

test('submitContactForm handles email sending failure', async () => {
  const formData = new FormData();
  formData.append('name', 'Test User');
  formData.append('email', '<EMAIL>'); // This email will trigger a failure in the mock
  formData.append('message', 'This is a test message.');

  const result = await submitContactForm(null, formData);

  expect(result).toEqual({ success: false, message: 'Submission failed' });
});