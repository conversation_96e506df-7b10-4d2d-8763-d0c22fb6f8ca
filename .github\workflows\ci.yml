name: CI

on:
  push:
    branches:
      - main

jobs:
  build-and-test:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20'

      - name: Install dependencies
        run: npm ci

      - name: Run Vitest unit tests
        run: npm test

      - name: Install Playwright Browsers
        run: npx playwright install --with-deps

      - name: Run Playwright E2E tests
        run: npx playwright test

      - name: Run Axe accessibility scans (Playwright)
        run: npx playwright test --project=chromium --reporter=list --grep "accessibility"
      - name: Run Lighthouse CI
        run: npx lhci autorun