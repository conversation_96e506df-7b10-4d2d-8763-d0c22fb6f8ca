@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: 255 255 255;
  --foreground: 10 31 68;  /* primary color for text */
  --card: 255 255 255;
  --card-foreground: 10 31 68;
  --popover: 255 255 255;
  --popover-foreground: 10 31 68;
  --primary: 10 31 68;     /* #0A1F44 */
  --primary-foreground: 255 255 255;
  --secondary: 31 122 255; /* #1F7AFF */
  --secondary-foreground: 255 255 255;
  --muted: 248 250 252;
  --muted-foreground: 71 85 105;
  --accent: 59 240 179;    /* #3BF0B3 */
  --accent-foreground: 16 22 35; /* surface color for contrast */
  --destructive: 239 68 68;
  --destructive-foreground: 255 255 255;
  --border: 226 232 240;
  --input: 226 232 240;
  --ring: 31 122 255;      /* secondary color for focus rings */
  --chart-1: 10 31 68;     /* primary */
  --chart-2: 31 122 255;   /* secondary */
  --chart-3: 110 50 201;   /* tertiary */
  --chart-4: 59 240 179;   /* accent */
  --chart-5: 16 22 35;     /* surface */
  --sidebar: 255 255 255;
  --sidebar-foreground: 10 31 68;
  --sidebar-primary: 10 31 68;
  --sidebar-primary-foreground: 255 255 255;
  --sidebar-accent: 0 216 180;
  --sidebar-accent-foreground: 255 255 255;
  --sidebar-border: 226 232 240;
  --sidebar-ring: 0 216 180;
}

.dark {
  --background: 16 22 35;   /* surface color */
  --foreground: 255 255 255;
  --card: 10 31 68;         /* primary color for cards */
  --card-foreground: 255 255 255;
  --popover: 16 22 35;
  --popover-foreground: 255 255 255;
  --primary: 10 31 68;      /* #0A1F44 */
  --primary-foreground: 255 255 255;
  --secondary: 31 122 255;  /* #1F7AFF */
  --secondary-foreground: 255 255 255;
  --muted: 51 65 85;
  --muted-foreground: 148 163 184;
  --accent: 59 240 179;     /* #3BF0B3 */
  --accent-foreground: 16 22 35;
  --destructive: 239 68 68;
  --destructive-foreground: 255 255 255;
  --border: 51 65 85;
  --input: 51 65 85;
  --ring: 31 122 255;       /* secondary color for focus rings */
  --chart-1: 10 31 68;      /* primary */
  --chart-2: 31 122 255;    /* secondary */
  --chart-3: 110 50 201;    /* tertiary */
  --chart-4: 59 240 179;    /* accent */
  --chart-5: 16 22 35;      /* surface */
  --sidebar: 16 22 35;
  --sidebar-foreground: 255 255 255;
  --sidebar-primary: 10 31 68;
  --sidebar-primary-foreground: 255 255 255;
  --sidebar-accent: 59 240 179;
  --sidebar-accent-foreground: 16 22 35;
  --sidebar-border: 51 65 85;
  --sidebar-ring: 31 122 255;
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
  /* Enhanced focus rings for keyboard navigation */
  *:focus-visible {
    outline: 2px solid var(--color-accent); /* Using accent color for focus */
    outline-offset: 2px;
    border-radius: 3px; /* Slightly rounded corners for focus outline */
  }

  /* Skip-nav link styling */
  .skip-nav {
    position: absolute;
    top: -999px;
    left: -999px;
    width: 1px;
    height: 1px;
    overflow: hidden;
    z-index: 9999; /* Ensure it's on top */
    background-color: var(--color-primary); /* Use primary color for background */
    color: var(--color-primary-foreground); /* Use primary-foreground for text */
    padding: 8px;
    border-radius: 4px;
    text-decoration: none;
  }

  .skip-nav:focus {
    position: static;
    width: auto;
    height: auto;
    left: auto;
    top: auto;
    text-align: center;
  }
}
