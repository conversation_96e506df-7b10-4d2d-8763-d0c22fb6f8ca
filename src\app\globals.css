@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: 255 255 255;
  --foreground: 30 33 93;
  --card: 255 255 255;
  --card-foreground: 30 33 93;
  --popover: 255 255 255;
  --popover-foreground: 30 33 93;
  --primary: 30 33 93;
  --primary-foreground: 255 255 255;
  --secondary: 0 216 180;
  --secondary-foreground: 255 255 255;
  --muted: 248 250 252;
  --muted-foreground: 71 85 105;
  --accent: 0 216 180;
  --accent-foreground: 255 255 255;
  --destructive: 239 68 68;
  --destructive-foreground: 255 255 255;
  --border: 226 232 240;
  --input: 226 232 240;
  --ring: 0 216 180;
  --chart-1: 30 33 93;
  --chart-2: 0 216 180;
  --chart-3: 42 45 115;
  --chart-4: 26 224 196;
  --chart-5: 22 163 139;
  --sidebar: 255 255 255;
  --sidebar-foreground: 30 33 93;
  --sidebar-primary: 30 33 93;
  --sidebar-primary-foreground: 255 255 255;
  --sidebar-accent: 0 216 180;
  --sidebar-accent-foreground: 255 255 255;
  --sidebar-border: 226 232 240;
  --sidebar-ring: 0 216 180;
}

.dark {
  --background: 15 23 42;
  --foreground: 248 250 252;
  --card: 30 41 59;
  --card-foreground: 248 250 252;
  --popover: 30 41 59;
  --popover-foreground: 248 250 252;
  --primary: 42 45 115;
  --primary-foreground: 255 255 255;
  --secondary: 22 163 139;
  --secondary-foreground: 255 255 255;
  --muted: 51 65 85;
  --muted-foreground: 148 163 184;
  --accent: 0 216 180;
  --accent-foreground: 15 23 42;
  --destructive: 239 68 68;
  --destructive-foreground: 255 255 255;
  --border: 51 65 85;
  --input: 51 65 85;
  --ring: 0 216 180;
  --chart-1: 42 45 115;
  --chart-2: 0 216 180;
  --chart-3: 30 33 93;
  --chart-4: 26 224 196;
  --chart-5: 22 163 139;
  --sidebar: 30 41 59;
  --sidebar-foreground: 248 250 252;
  --sidebar-primary: 42 45 115;
  --sidebar-primary-foreground: 255 255 255;
  --sidebar-accent: 0 216 180;
  --sidebar-accent-foreground: 15 23 42;
  --sidebar-border: 51 65 85;
  --sidebar-ring: 0 216 180;
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
  /* Enhanced focus rings for keyboard navigation */
  *:focus-visible {
    outline: 2px solid var(--color-accent); /* Using accent color for focus */
    outline-offset: 2px;
    border-radius: 3px; /* Slightly rounded corners for focus outline */
  }

  /* Skip-nav link styling */
  .skip-nav {
    position: absolute;
    top: -999px;
    left: -999px;
    width: 1px;
    height: 1px;
    overflow: hidden;
    z-index: 9999; /* Ensure it's on top */
    background-color: var(--color-primary); /* Use primary color for background */
    color: var(--color-primary-foreground); /* Use primary-foreground for text */
    padding: 8px;
    border-radius: 4px;
    text-decoration: none;
  }

  .skip-nav:focus {
    position: static;
    width: auto;
    height: auto;
    left: auto;
    top: auto;
    text-align: center;
  }
}
