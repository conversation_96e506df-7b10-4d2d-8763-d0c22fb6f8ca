'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { ArrowRight, Sparkles, Zap, Target } from 'lucide-react';
import { GeminiDemo } from '@/components/GeminiDemo';

export default function HomeContent() {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
  };

  const features = [
    {
      icon: <Sparkles className="w-6 h-6" />,
      title: "AI Platforms",
      description: "Multi-model integration and intelligent routing"
    },
    {
      icon: <Zap className="w-6 h-6" />,
      title: "Automation",
      description: "Intelligent workflows and process optimization"
    },
    {
      icon: <Target className="w-6 h-6" />,
      title: "Custom Solutions",
      description: "Tailored software for your unique challenges"
    }
  ];

  return (
    <motion.main
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="min-h-screen flex flex-col"
    >
      {/* Hero Section */}
      <section className="relative flex-1 flex items-center justify-center px-6 lg:px-24 py-20 overflow-hidden">
        {/* Gradient Background with 45° angle */}
        <div className="absolute inset-0 bg-gradient-to-br from-primary via-secondary to-tertiary opacity-90" />

        {/* Noise Overlay */}
        <div
          className="absolute inset-0 opacity-20 mix-blend-overlay"
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.9' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E")`,
          }}
        />

        <div className="relative z-10 max-w-7xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left Column - Hero Content */}
            <div className="text-center lg:text-left">
              <motion.div
                variants={itemVariants}
                className="mb-8"
              >
                <h1 className="text-4xl md:text-6xl lg:text-7xl font-extrabold mb-6 text-white leading-tight">
                  Engineering Tomorrow&apos;s Intelligence
                </h1>
                <p className="text-xl md:text-2xl text-white/90 max-w-2xl mx-auto lg:mx-0 leading-relaxed">
                  We pioneer advancements in artificial intelligence and intelligent automation,
                  crafting bespoke software solutions that drive transformative growth and efficiency.
                </p>
              </motion.div>

              <motion.div
                variants={itemVariants}
                className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start items-center"
              >
                <Link href="/projects">
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="group px-8 py-4 bg-accent text-surface font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 flex items-center gap-2"
                  >
                    View Our Work
                    <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
                  </motion.div>
                </Link>
                <Link href="/contact">
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="group px-8 py-4 bg-white/10 backdrop-blur-sm text-white border border-white/20 font-semibold rounded-lg shadow-lg hover:shadow-xl hover:bg-white/20 transition-all duration-200 flex items-center gap-2"
                  >
                    Contact Us
                    <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
                  </motion.div>
                </Link>
              </motion.div>
            </div>

            {/* Right Column - Gemini Demo */}
            <motion.div
              variants={itemVariants}
              className="flex justify-center lg:justify-end"
            >
              <div className="w-full max-w-md">
                <GeminiDemo />
              </div>
            </motion.div>
          </div>
        </div>
      </section>


      {/* Feature Cards Section */}
      <motion.section
        variants={itemVariants}
        className="py-20 px-6 lg:px-24 bg-surface-alt dark:bg-surface"
      >
        <div className="max-w-6xl mx-auto">
          <motion.div
            variants={containerVariants}
            className="grid md:grid-cols-3 gap-8"
          >
            {features.map((feature, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                className="group p-8 rounded-2xl bg-white dark:bg-surface-alt border border-border hover:border-secondary/20 hover:shadow-lg transition-all duration-300"
              >
                <div className="flex flex-col items-center text-center space-y-4">
                  <div className="p-4 rounded-xl bg-secondary/10 text-secondary group-hover:bg-secondary group-hover:text-white transition-colors">
                    {feature.icon}
                  </div>
                  <h2 className="text-xl font-semibold text-primary dark:text-white">{feature.title}</h2>
                  <p className="text-muted-foreground">{feature.description}</p>
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </motion.section>

      {/* Stats Section */}
      <motion.section
        variants={itemVariants}
        className="py-16 bg-muted/30"
      >
        <div className="max-w-6xl mx-auto px-6 lg:px-24">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-3xl md:text-4xl font-bold text-primary mb-2">6+</div>
              <div className="text-sm text-muted-foreground">Flagship Projects</div>
            </div>
            <div>
              <div className="text-3xl md:text-4xl font-bold text-primary mb-2">50%</div>
              <div className="text-sm text-muted-foreground">Faster Development</div>
            </div>
            <div>
              <div className="text-3xl md:text-4xl font-bold text-primary mb-2">99.9%</div>
              <div className="text-sm text-muted-foreground">System Uptime</div>
            </div>
            <div>
              <div className="text-3xl md:text-4xl font-bold text-primary mb-2">24h</div>
              <div className="text-sm text-muted-foreground">Response Time</div>
            </div>
          </div>
        </div>
      </motion.section>

      {/* CTA Section */}
      <motion.section
        variants={itemVariants}
        className="py-20 px-6 lg:px-24 bg-gradient-to-r from-primary/5 via-secondary/5 to-tertiary/5"
      >
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6 text-primary dark:text-white">
            Ready to Transform Your Business?
          </h2>
          <p className="text-xl text-muted-foreground mb-8">
            Let&apos;s discuss how our AI expertise can accelerate your growth and innovation.
          </p>
          <Link href="/contact">
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="inline-flex items-center gap-2 px-8 py-4 bg-accent text-surface font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all duration-200"
            >
              Start Your Project
              <ArrowRight className="w-4 h-4" />
            </motion.div>
          </Link>
        </div>
      </motion.section>
    </motion.main>
  );
}
