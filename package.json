{"name": "metamorphic-labs-portfolio", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "postbuild": "next-sitemap", "start": "next start", "lint": "next lint", "prepare": "husky", "test": "vitest", "test:e2e": "playwright test"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"]}, "dependencies": {"@fontsource/inter": "^5.2.6", "@fontsource/jetbrains-mono": "^5.2.6", "@google/generative-ai": "^0.24.1", "@hookform/resolvers": "^5.1.1", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@supabase/supabase-js": "^2.50.0", "@vercel/analytics": "^1.5.0", "@vercel/og": "^0.6.8", "@vercel/speed-insights": "^1.2.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.18.1", "lucide-react": "^0.522.0", "next": "15.3.4", "next-mdx-remote": "^5.0.0", "next-sitemap": "^4.2.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.58.1", "shadcn-ui": "^0.9.5", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "web-vitals": "^5.0.3", "zod": "^3.25.67"}, "devDependencies": {"@axe-core/playwright": "^4.10.2", "@eslint/eslintrc": "^3", "@lhci/cli": "^0.15.0", "@playwright/test": "^1.53.1", "@tailwindcss/line-clamp": "^0.4.4", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/node": "^20.19.1", "@types/react": "^19", "@types/react-dom": "^19", "@types/testing-library__jest-dom": "^5.14.9", "@vitejs/plugin-react": "^4.5.2", "eslint": "^9", "eslint-config-next": "15.3.4", "husky": "^9.1.7", "jsdom": "^26.1.0", "lint-staged": "^16.1.2", "next-themes": "^0.4.6", "prettier": "^3.5.3", "tailwindcss": "^4", "tw-animate-css": "^1.3.4", "typescript": "^5", "vitest": "^3.2.4"}}