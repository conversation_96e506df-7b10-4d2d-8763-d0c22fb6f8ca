import { test, expect } from '@playwright/test';

test.describe('Performance Tests', () => {
  test('should meet Core Web Vitals thresholds', async ({ page }) => {
    // Navigate to homepage
    await page.goto('/');
    
    // Wait for page to load completely
    await page.waitForLoadState('networkidle');
    
    // Measure Core Web Vitals
    const vitals = await page.evaluate(() => {
      return new Promise((resolve) => {
        const vitals: any = {};
        
        // Largest Contentful Paint (LCP)
        new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1];
          vitals.lcp = lastEntry.startTime;
        }).observe({ entryTypes: ['largest-contentful-paint'] });
        
        // First Input Delay (FID) - simulate with click
        document.addEventListener('click', () => {
          vitals.fid = performance.now();
        }, { once: true });
        
        // Cumulative Layout Shift (CLS)
        let clsValue = 0;
        new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (!(entry as any).hadRecentInput) {
              clsValue += (entry as any).value;
            }
          }
          vitals.cls = clsValue;
        }).observe({ entryTypes: ['layout-shift'] });
        
        // First Contentful Paint (FCP)
        new PerformanceObserver((list) => {
          const entries = list.getEntries();
          vitals.fcp = entries[0].startTime;
        }).observe({ entryTypes: ['paint'] });
        
        // Time to First Byte (TTFB)
        const navigationEntry = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        vitals.ttfb = navigationEntry.responseStart - navigationEntry.requestStart;
        
        setTimeout(() => resolve(vitals), 3000);
      });
    });
    
    console.log('Core Web Vitals:', vitals);
    
    // Assert Core Web Vitals thresholds
    // LCP should be less than 2.5 seconds (2500ms)
    if (vitals.lcp) {
      expect(vitals.lcp).toBeLessThan(2500);
    }
    
    // FCP should be less than 1.8 seconds (1800ms)
    if (vitals.fcp) {
      expect(vitals.fcp).toBeLessThan(1800);
    }
    
    // CLS should be less than 0.1
    if (vitals.cls !== undefined) {
      expect(vitals.cls).toBeLessThan(0.1);
    }
    
    // TTFB should be less than 600ms
    if (vitals.ttfb) {
      expect(vitals.ttfb).toBeLessThan(600);
    }
  });

  test('should load critical resources quickly', async ({ page }) => {
    const startTime = Date.now();
    
    await page.goto('/');
    
    // Wait for critical elements to be visible
    await expect(page.locator('h1')).toBeVisible();
    await expect(page.locator('nav')).toBeVisible();
    
    const loadTime = Date.now() - startTime;
    
    // Page should load critical content within 2 seconds
    expect(loadTime).toBeLessThan(2000);
  });

  test('should have optimized images', async ({ page }) => {
    await page.goto('/projects');
    
    // Check that images are properly optimized
    const images = await page.locator('img').all();
    
    for (const img of images) {
      const src = await img.getAttribute('src');
      const loading = await img.getAttribute('loading');
      
      // Images should use modern formats or be lazy loaded
      if (src) {
        const isOptimized = src.includes('.webp') || 
                          src.includes('.avif') || 
                          loading === 'lazy' ||
                          src.startsWith('/_next/image');
        
        expect(isOptimized).toBeTruthy();
      }
    }
  });

  test('should have minimal render-blocking resources', async ({ page }) => {
    const response = await page.goto('/');
    
    // Check response headers for optimization
    const headers = response?.headers();
    
    // Should have compression
    expect(headers?.['content-encoding']).toBeTruthy();
    
    // Should have caching headers for static assets
    if (headers?.['content-type']?.includes('javascript') || 
        headers?.['content-type']?.includes('css')) {
      expect(headers?.['cache-control']).toBeTruthy();
    }
  });

  test('should have fast navigation between pages', async ({ page }) => {
    await page.goto('/');
    
    // Measure navigation time to different pages
    const pages = ['/about', '/expertise', '/projects', '/contact'];
    
    for (const targetPage of pages) {
      const startTime = Date.now();
      
      await page.click(`a[href="${targetPage}"]`);
      await expect(page.locator('h1')).toBeVisible();
      
      const navigationTime = Date.now() - startTime;
      
      // Client-side navigation should be fast (< 500ms)
      expect(navigationTime).toBeLessThan(500);
      
      // Go back to home for next test
      await page.goto('/');
    }
  });

  test('should handle slow network conditions', async ({ page, context }) => {
    // Simulate slow 3G network
    await context.route('**/*', async (route) => {
      await new Promise(resolve => setTimeout(resolve, 100)); // Add 100ms delay
      await route.continue();
    });
    
    const startTime = Date.now();
    await page.goto('/');
    
    // Even on slow network, critical content should load within reasonable time
    await expect(page.locator('h1')).toBeVisible({ timeout: 10000 });
    
    const loadTime = Date.now() - startTime;
    expect(loadTime).toBeLessThan(10000); // 10 seconds max on slow network
  });

  test('should have efficient bundle sizes', async ({ page }) => {
    // Monitor network requests
    const requests: any[] = [];
    
    page.on('request', request => {
      requests.push({
        url: request.url(),
        resourceType: request.resourceType()
      });
    });
    
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Check JavaScript bundle sizes
    const jsRequests = requests.filter(req => 
      req.resourceType === 'script' && req.url.includes('/_next/static/')
    );
    
    // Should not have too many JavaScript chunks
    expect(jsRequests.length).toBeLessThan(10);
    
    // Check CSS bundle sizes
    const cssRequests = requests.filter(req => 
      req.resourceType === 'stylesheet'
    );
    
    // Should have minimal CSS requests
    expect(cssRequests.length).toBeLessThan(5);
  });

  test('should preload critical resources', async ({ page }) => {
    await page.goto('/');
    
    // Check for preload links in head
    const preloadLinks = await page.locator('link[rel="preload"]').count();
    
    // Should have some preloaded resources (fonts, critical images, etc.)
    expect(preloadLinks).toBeGreaterThan(0);
  });

  test('should have proper caching strategy', async ({ page }) => {
    // First visit
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Second visit (should use cache)
    const startTime = Date.now();
    await page.reload();
    await expect(page.locator('h1')).toBeVisible();
    const reloadTime = Date.now() - startTime;
    
    // Reload should be faster due to caching
    expect(reloadTime).toBeLessThan(1000);
  });

  test('should handle concurrent users efficiently', async ({ browser }) => {
    // Simulate multiple concurrent users
    const contexts = await Promise.all([
      browser.newContext(),
      browser.newContext(),
      browser.newContext()
    ]);
    
    const pages = await Promise.all(
      contexts.map(context => context.newPage())
    );
    
    // All pages should load simultaneously without issues
    const loadPromises = pages.map(async (page, index) => {
      const startTime = Date.now();
      await page.goto('/');
      await expect(page.locator('h1')).toBeVisible();
      return Date.now() - startTime;
    });
    
    const loadTimes = await Promise.all(loadPromises);
    
    // All pages should load within reasonable time
    loadTimes.forEach(time => {
      expect(time).toBeLessThan(3000);
    });
    
    // Cleanup
    await Promise.all(contexts.map(context => context.close()));
  });
});
