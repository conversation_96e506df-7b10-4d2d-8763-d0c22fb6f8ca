import { test, expect } from '@playwright/test';
import AxeBuilder from '@axe-core/playwright'; // Import AxeBuilder

test.describe('Contact Form', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/contact');
  });

  test('should not have any automatically detectable accessibility issues', async ({ page }) => {
    const accessibilityScanResults = await new AxeBuilder({ page }).analyze();
    expect(accessibilityScanResults.violations).toEqual([]);
  });

  test('submit contact form successfully', async ({ page }) => {
    // Step 1: Fill contact information
    await page.fill('input[name="name"]', 'Test User');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="company"]', 'Test Company');
    await page.click('button:has-text("Next Step")');

    // Step 2: Fill project details
    await page.selectOption('select[name="projectType"]', 'ai-platform');
    await page.selectOption('select[name="budget"]', '25k-50k');
    await page.selectOption('select[name="timeline"]', '3-6months');
    await page.click('button:has-text("Next Step")');

    // Step 3: Fill message and accept NDA
    await page.fill('textarea[name="message"]', 'This is a test message for our AI platform project.');
    await page.check('input[name="ndaAccepted"]');
    await page.click('button[type="submit"]');

    // Wait for success message
    await expect(page.locator('[data-sonner-toast]')).toContainText('Thank you for your message!');
  });

  test('show validation errors for empty required fields', async ({ page }) => {
    // Try to proceed without filling required fields
    await page.click('button:has-text("Next Step")');

    // Should show validation errors for required fields
    await expect(page.locator('text=Name must be at least 2 characters long')).toBeVisible();
    await expect(page.locator('text=Please enter a valid email address')).toBeVisible();
  });

  test('show error for invalid email format', async ({ page }) => {
    await page.fill('input[name="name"]', 'Test User');
    await page.fill('input[name="email"]', 'invalid-email');
    await page.click('button:has-text("Next Step")');

    await expect(page.locator('text=Please enter a valid email address')).toBeVisible();
  });

  test('multi-step form navigation works correctly', async ({ page }) => {
    // Fill step 1
    await page.fill('input[name="name"]', 'Test User');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.click('button:has-text("Next Step")');

    // Should be on step 2
    await expect(page.locator('select[name="projectType"]')).toBeVisible();

    // Go back to step 1
    await page.click('button:has-text("Previous")');

    // Should be back on step 1
    await expect(page.locator('input[name="name"]')).toBeVisible();
    await expect(page.locator('input[name="name"]')).toHaveValue('Test User');
  });
});