'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { ThemeToggle } from './ThemeToggle';

// Define navigation links
const navLinks = [
  { href: '/', label: 'Home' },
  { href: '/about', label: 'About' },
  { href: '/expertise', label: 'Expertise' },
  { href: '/projects', label: 'Projects' },
  { href: '/contact', label: 'Contact' },
];

export function Layout({ children }: { children: React.ReactNode }) {
  return (
    <div className="flex flex-col min-h-screen font-body">
      <a href="#main" className="skip-nav">Skip to content</a>
      <header className="bg-white dark:bg-gray-900 text-gray-800 dark:text-white p-4 shadow-md">
        <nav className="container mx-auto flex justify-between items-center">
          {/* Logo/Company Name */}
          <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
            <Link href="/" className="text-2xl font-bold font-code" aria-label="Metamorphic Reactor Home">
              Metamorphic Reactor
            </Link>
          </motion.div>

          {/* Navigation Links */}
          <div className="flex space-x-6">
            {navLinks.map((link) => (
              <motion.div
                key={link.href}
                className="relative"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Link
                  href={link.href}
                  className="relative inline-block px-2 py-1 transition-colors duration-300
                             text-gray-800 dark:text-white hover:text-primary dark:hover:text-accent"
                >
                  {link.label}
                </Link>
                {/* Framer Motion animated underline */}
                <motion.div
                  className="absolute bottom-0 left-0 w-full h-0.5 bg-primary dark:bg-accent origin-center"
                  initial={{ scaleX: 0 }}
                  whileHover={{ scaleX: 1 }}
                  transition={{ duration: 0.3, ease: "easeInOut" }}
                />
              </motion.div>
            ))}
          </div>

          {/* Theme Toggle */}
          <ThemeToggle />
        </nav>
      </header>

      <main id="main" className="flex-grow container mx-auto p-4">
        {children}
      </main>

      <footer className="bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-300 p-8 mt-auto">
        <div className="container mx-auto text-center">
          {/* Copyright */}
          <p className="text-sm">
            &copy; {new Date().getFullYear()} Metamorphic Reactor. All rights reserved.
          </p>

          {/* Social Media Links */}
          <div className="flex justify-center space-x-6 mt-4">
            {/* Placeholder links */}
            <Link href="#" className="hover:text-primary dark:hover:text-accent transition-colors duration-300" aria-label="LinkedIn Profile">LinkedIn</Link>
            <Link href="#" className="hover:text-primary dark:hover:text-accent transition-colors duration-300" aria-label="GitHub Profile">GitHub</Link>
            <Link href="#" className="hover:text-primary dark:hover:text-accent transition-colors duration-300" aria-label="Twitter Profile">Twitter</Link>
          </div>

          {/* Company Mission Snippet */}
          <p className="mt-4 text-sm">
            Our mission is to accelerate innovation through intelligent automation and collaborative AI.
          </p>
        </div>
      </footer>
    </div>
  );
}