import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Terms of Service | Metamorphic Labs',
  description: 'Terms of Service for Metamorphic Labs - Legal terms and conditions for using our services.',
};

export default function TermsPage() {
  return (
    <main className="min-h-screen py-16 px-6 lg:px-24">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-4xl font-bold mb-8">Terms of Service</h1>
        
        <div className="prose prose-lg max-w-none space-y-6">
          <p className="text-lg text-muted-foreground">
            Last updated: {new Date().toLocaleDateString()}
          </p>
          
          <section>
            <h2 className="text-2xl font-semibold mb-4">Acceptance of Terms</h2>
            <p>
              By accessing and using the services provided by Metamorphic Labs LLC, 
              you accept and agree to be bound by the terms and provision of this agreement.
            </p>
          </section>

          <section>
            <h2 className="text-2xl font-semibold mb-4">Services</h2>
            <p>
              Metamorphic Labs provides artificial intelligence consulting, software development, 
              and automation services. All services are provided subject to these terms.
            </p>
          </section>

          <section>
            <h2 className="text-2xl font-semibold mb-4">Intellectual Property</h2>
            <p>
              All intellectual property rights in our services and materials remain with 
              Metamorphic Labs LLC unless otherwise specified in a separate agreement.
            </p>
          </section>

          <section>
            <h2 className="text-2xl font-semibold mb-4">Limitation of Liability</h2>
            <p>
              Metamorphic Labs LLC shall not be liable for any indirect, incidental, 
              special, consequential, or punitive damages arising out of your use of our services.
            </p>
          </section>

          <section>
            <h2 className="text-2xl font-semibold mb-4">Contact Information</h2>
            <p>
              For questions about these Terms of Service, please contact us at{' '}
              <a href="mailto:<EMAIL>" className="text-primary hover:underline">
                <EMAIL>
              </a>
            </p>
          </section>
        </div>
      </div>
    </main>
  );
}
