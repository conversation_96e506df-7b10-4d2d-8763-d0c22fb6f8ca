import { supabase } from '../../src/lib/supabaseClient';

const projects = [
  {
    slug: 'ai-platform',
    title: 'Enterprise AI Platform',
    summary: 'Next-gen AI infrastructure for enterprise',
    body_md: `## Project Overview

This project focuses on developing a cutting-edge Enterprise AI Platform designed to streamline and enhance business operations through advanced artificial intelligence. The platform integrates various AI models, including machine learning, natural language processing, and computer vision, to provide comprehensive solutions for data analysis, automation, and predictive insights.

### Key Features:

- **Scalable Architecture**: Built on a robust and scalable infrastructure, the platform can handle large volumes of data and complex AI workloads, ensuring high performance and reliability.
- **Customizable Modules**: Offers a suite of customizable AI modules that can be tailored to specific business needs, allowing for flexible deployment across different departments and industries.
- **Real-time Analytics**: Provides real-time data processing and analytical capabilities, enabling businesses to make informed decisions quickly and efficiently.
- **Secure Data Handling**: Implements stringent security protocols to protect sensitive enterprise data, ensuring compliance with industry standards and regulations.
- **User-Friendly Interface**: Features an intuitive and user-friendly interface, making it accessible to both technical and non-technical users.

### Technologies Used:

- **Backend**: Node.js, Python, FastAPI
- **Database**: PostgreSQL, Supabase
- **AI/ML Frameworks**: TensorFlow, PyTorch, scikit-learn
- **Cloud Platform**: AWS, Azure, Google Cloud
- **Containerization**: Docker, Kubernetes

### Impact:

The Enterprise AI Platform aims to revolutionize how businesses leverage AI, driving innovation, improving operational efficiency, and unlocking new opportunities for growth. By automating repetitive tasks, providing deeper insights, and enhancing decision-making processes, the platform empowers organizations to stay competitive in today's rapidly evolving market.`,
    metrics_json: { accuracy: '98%', speedup: '10x' },
    hero_url: '/project-ai.jpg'
  },
  {
    slug: 'blockchain-solution',
    title: 'Decentralized Blockchain Solution',
    summary: 'Secure and transparent blockchain for supply chain',
    body_md: `## Project Overview

This project involves the development of a Decentralized Blockchain Solution aimed at enhancing transparency and security within supply chain management. By leveraging blockchain technology, the solution provides an immutable ledger for tracking goods, ensuring authenticity, and reducing fraud.

### Key Features:

- **Immutable Ledger**: All transactions and data entries are recorded on an immutable ledger, providing a verifiable and tamper-proof history of goods movement.
- **Smart Contracts**: Utilizes smart contracts to automate and enforce agreements between parties, reducing the need for intermediaries and increasing efficiency.
- **Enhanced Security**: Employs cryptographic techniques to secure data and transactions, protecting against unauthorized access and manipulation.
- **Supply Chain Transparency**: Offers end-to-end visibility into the supply chain, allowing stakeholders to track products from origin to destination.
- **Interoperability**: Designed to be interoperable with existing supply chain systems, facilitating seamless integration and data exchange.

### Technologies Used:

- **Blockchain Platform**: Ethereum, Hyperledger Fabric
- **Smart Contract Language**: Solidity
- **Backend**: Node.js, Go
- **Database**: IPFS, MongoDB
- **Cloud Platform**: AWS, Azure

### Impact:

The Decentralized Blockchain Solution transforms traditional supply chain operations by introducing a new level of trust, efficiency, and transparency. It helps businesses mitigate risks, improve accountability, and build stronger relationships with their partners and customers.`,
    metrics_json: { transactions_per_sec: '1000', security_rating: 'A+' },
    hero_url: '/project-blockchain.jpg'
  },
  {
    slug: 'quantum-computing-research',
    title: 'Quantum Computing Research',
    summary: 'Exploring new algorithms for quantum supremacy',
    body_md: `## Project Overview

This research project delves into the exciting field of Quantum Computing, focusing on the exploration and development of novel algorithms that push the boundaries of computational power. The goal is to contribute to the understanding and achievement of quantum supremacy, where quantum computers can solve problems intractable for classical computers.

### Key Areas of Research:

- **Quantum Algorithm Design**: Developing new quantum algorithms for various applications, including optimization, cryptography, and simulation.
- **Quantum Error Correction**: Investigating methods to mitigate errors in quantum computations, a critical challenge for building fault-tolerant quantum computers.
- **Quantum Machine Learning**: Exploring the intersection of quantum computing and machine learning to develop more powerful AI models.
- **Quantum Hardware Integration**: Collaborating with hardware teams to optimize algorithms for specific quantum architectures.
- **Theoretical Foundations**: Contributing to the theoretical understanding of quantum mechanics and its implications for computation.

### Technologies Used:

- **Quantum Programming Languages**: Qiskit, Cirq
- **Quantum Simulators**: IBM Quantum Experience, Google Cirq Simulator
- **Mathematical Tools**: NumPy, SciPy
- **Cloud Platforms**: AWS Braket, Azure Quantum

### Impact:

The Quantum Computing Research project aims to advance the state-of-the-art in quantum computing, paving the way for revolutionary breakthroughs in various scientific and industrial domains. By pushing the limits of computation, this research has the potential to solve some of the world's most complex problems, from drug discovery to financial modeling.`,
    metrics_json: { qubits_simulated: '64', algorithm_efficiency: 'high' },
    hero_url: '/project-quantum.jpg'
  }
];

async function seed() {
  console.log('Seeding projects...');
  for (const project of projects) {
    const { error } = await supabase.from('projects').upsert(project);
    if (error) {
      console.error(`Error seeding ${project.slug}:`, error);
    } else {
      console.log(`Successfully seeded project: ${project.slug}`);
    }
  }
  console.log('Project seeding complete.');
}

seed();