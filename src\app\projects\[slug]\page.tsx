import { notFound } from 'next/navigation';
import { Metadata } from 'next';
import { supabase } from '../../../lib/supabaseClient';
import ProjectDetailContent from './ProjectDetailContent';

export async function generateMetadata({ params }: { params: Promise<{ slug: string }> }): Promise<Metadata> {
  const { slug } = await params;
  const { data: project } = await supabase
    .from('projects')
    .select('title, summary, hero_url')
    .eq('slug', slug)
    .single();

  if (!project) {
    return {
      title: 'Project Not Found | Metamorphic Labs',
    };
  }

  return {
    title: `${project.title} | Metamorphic Labs`,
    description: project.summary,
    openGraph: {
      title: project.title,
      description: project.summary,
      type: 'article',
      images: [
        {
          url: `https://metamorphiclabs.com${project.hero_url}`,
          width: 1200,
          height: 630,
          alt: project.title,
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: project.title,
      description: project.summary,
      images: [`https://metamorphiclabs.com${project.hero_url}`],
    },
  };
}

export async function generateStaticParams() {
  const { data: projects } = await supabase
    .from('projects')
    .select('slug');

  return projects?.map((project) => ({
    slug: project.slug,
  })) || [];
}

export default async function ProjectPage({ params }: { params: Promise<{ slug: string }> }) {
  const { slug } = await params;
  const { data: project } = await supabase
    .from('projects')
    .select('*')
    .eq('slug', slug)
    .single();

  if (!project) return notFound();

  // Get related projects (same tags, excluding current project)
  const { data: relatedProjects } = await supabase
    .from('projects')
    .select('slug, title, summary, status, hero_url, tags')
    .neq('slug', slug)
    .limit(3);

  return <ProjectDetailContent project={project} relatedProjects={relatedProjects || []} />;
}