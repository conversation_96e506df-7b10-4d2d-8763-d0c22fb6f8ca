import { test, expect } from '@playwright/test';
import AxeBuilder from '@axe-core/playwright';

test.describe('Comprehensive Accessibility Tests', () => {
  const pages = [
    { url: '/', name: 'Homepage' },
    { url: '/about', name: 'About Page' },
    { url: '/expertise', name: 'Expertise Page' },
    { url: '/projects', name: 'Projects Page' },
    { url: '/contact', name: 'Contact Page' },
    { url: '/projects/metamorphic-ai-platform', name: 'Project Detail Page' },
  ];

  pages.forEach(({ url, name }) => {
    test(`${name} should not have accessibility violations`, async ({ page }) => {
      await page.goto(url);
      
      // Wait for page to load
      await expect(page.locator('h1')).toBeVisible();
      
      const accessibilityScanResults = await new AxeBuilder({ page })
        .withTags(['wcag2a', 'wcag2aa', 'wcag21aa', 'wcag22aa'])
        .analyze();
      
      expect(accessibilityScanResults.violations).toEqual([]);
    });
  });

  test('should have proper heading hierarchy', async ({ page }) => {
    await page.goto('/');
    
    const headings = await page.locator('h1, h2, h3, h4, h5, h6').all();
    const headingLevels = await Promise.all(
      headings.map(async (heading) => {
        const tagName = await heading.evaluate(el => el.tagName.toLowerCase());
        return parseInt(tagName.charAt(1));
      })
    );

    // Check that we start with h1
    expect(headingLevels[0]).toBe(1);
    
    // Check that heading levels don't skip (e.g., h1 -> h3)
    for (let i = 1; i < headingLevels.length; i++) {
      const currentLevel = headingLevels[i];
      const previousLevel = headingLevels[i - 1];
      expect(currentLevel - previousLevel).toBeLessThanOrEqual(1);
    }
  });

  test('should have proper focus management', async ({ page }) => {
    await page.goto('/');
    
    // Test keyboard navigation
    await page.keyboard.press('Tab');
    
    // Should focus on first interactive element
    const focusedElement = await page.locator(':focus').first();
    await expect(focusedElement).toBeVisible();
    
    // Test that focus is visible
    const focusedElementBox = await focusedElement.boundingBox();
    expect(focusedElementBox).toBeTruthy();
  });

  test('should have proper color contrast', async ({ page }) => {
    await page.goto('/');
    
    const accessibilityScanResults = await new AxeBuilder({ page })
      .withTags(['wcag2aa'])
      .include('body')
      .analyze();
    
    const colorContrastViolations = accessibilityScanResults.violations.filter(
      violation => violation.id === 'color-contrast'
    );
    
    expect(colorContrastViolations).toEqual([]);
  });

  test('should have proper ARIA labels and roles', async ({ page }) => {
    await page.goto('/contact');
    
    // Check form has proper labels
    const formInputs = await page.locator('input, textarea, select').all();
    
    for (const input of formInputs) {
      const ariaLabel = await input.getAttribute('aria-label');
      const ariaLabelledBy = await input.getAttribute('aria-labelledby');
      const associatedLabel = await page.locator(`label[for="${await input.getAttribute('id')}"]`).count();
      
      // Each input should have either aria-label, aria-labelledby, or associated label
      expect(
        ariaLabel || ariaLabelledBy || associatedLabel > 0
      ).toBeTruthy();
    }
  });

  test('should be navigable with keyboard only', async ({ page }) => {
    await page.goto('/');
    
    // Get all interactive elements
    const interactiveElements = await page.locator(
      'a, button, input, textarea, select, [tabindex]:not([tabindex="-1"])'
    ).all();
    
    // Test that all interactive elements are reachable via keyboard
    for (let i = 0; i < Math.min(interactiveElements.length, 10); i++) {
      await page.keyboard.press('Tab');
      const focusedElement = await page.locator(':focus').first();
      await expect(focusedElement).toBeVisible();
    }
  });

  test('should have proper alt text for images', async ({ page }) => {
    await page.goto('/projects');
    
    const images = await page.locator('img').all();
    
    for (const img of images) {
      const alt = await img.getAttribute('alt');
      const ariaLabel = await img.getAttribute('aria-label');
      const role = await img.getAttribute('role');
      
      // Images should have alt text, aria-label, or be decorative (role="presentation")
      expect(
        alt !== null || ariaLabel !== null || role === 'presentation'
      ).toBeTruthy();
    }
  });

  test('should have proper form validation messages', async ({ page }) => {
    await page.goto('/contact');
    
    // Try to submit form without filling required fields
    await page.click('button:has-text("Next Step")');
    
    // Check that validation messages are properly associated with form fields
    const errorMessages = await page.locator('[role="alert"], .error-message, [aria-invalid="true"]').all();
    
    for (const errorMessage of errorMessages) {
      const isVisible = await errorMessage.isVisible();
      expect(isVisible).toBeTruthy();
    }
  });

  test('should support screen reader announcements', async ({ page }) => {
    await page.goto('/contact');
    
    // Check for live regions that announce dynamic content
    const liveRegions = await page.locator('[aria-live], [role="status"], [role="alert"]').count();
    
    // Should have at least one live region for form feedback
    expect(liveRegions).toBeGreaterThan(0);
  });

  test('should have proper landmark roles', async ({ page }) => {
    await page.goto('/');
    
    // Check for main landmarks
    await expect(page.locator('main, [role="main"]')).toBeVisible();
    await expect(page.locator('header, [role="banner"]')).toBeVisible();
    await expect(page.locator('footer, [role="contentinfo"]')).toBeVisible();
    await expect(page.locator('nav, [role="navigation"]')).toBeVisible();
  });

  test('should handle reduced motion preferences', async ({ page }) => {
    // Set reduced motion preference
    await page.emulateMedia({ reducedMotion: 'reduce' });
    await page.goto('/');
    
    // Check that animations are disabled or reduced
    const animatedElements = await page.locator('[style*="animation"], [style*="transition"]').count();
    
    // Page should still be functional with reduced motion
    await expect(page.locator('h1')).toBeVisible();
  });
});
