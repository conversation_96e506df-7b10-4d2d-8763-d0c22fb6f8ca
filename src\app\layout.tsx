import type { Metadata, Viewport } from "next";
import { Inter, JetBrains_Mono } from 'next/font/google';
import Layout from '../../components/Layout';
import PerformanceOptimizations from '../components/PerformanceOptimizations';
import './globals.css';
import { Analytics } from '@vercel/analytics/react';
import { SpeedInsights } from '@vercel/speed-insights/next';

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
  display: 'swap',
});

const jetbrainsMono = JetBrains_Mono({
  subsets: ['latin'],
  variable: '--font-jetbrains-mono',
  display: 'swap',
});

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 5,
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: '#1e215d' },
    { media: '(prefers-color-scheme: dark)', color: '#00d8b4' },
  ],
};

export const metadata: Metadata = {
  metadataBase: new URL('https://metamorphiclabs.com'),
  title: {
    default: 'Metamorphic Labs | Engineering Tomorrow\'s Intelligence',
    template: '%s | Metamorphic Labs',
  },
  description: 'Pioneering advancements in artificial intelligence and intelligent automation. We craft bespoke software solutions to drive transformative growth and efficiency for businesses worldwide.',
  keywords: [
    'artificial intelligence',
    'AI development',
    'machine learning',
    'intelligent automation',
    'custom software development',
    'AI consulting',
    'automation solutions',
    'AI platforms',
    'software engineering',
    'digital transformation'
  ],
  authors: [
    { name: 'Michael Laffin', url: 'https://metamorphiclabs.com/about' },
    { name: 'Rick Mellenberger', url: 'https://metamorphiclabs.com/about' }
  ],
  creator: 'Metamorphic Labs',
  publisher: 'Metamorphic Labs',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://metamorphiclabs.com',
    siteName: 'Metamorphic Labs',
    title: 'Metamorphic Labs | Engineering Tomorrow\'s Intelligence',
    description: 'Pioneering advancements in artificial intelligence and intelligent automation.',
    images: [
      {
        url: '/images/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'Metamorphic Labs - Engineering Tomorrow\'s Intelligence',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Metamorphic Labs | Engineering Tomorrow\'s Intelligence',
    description: 'Pioneering advancements in artificial intelligence and intelligent automation.',
    images: ['/images/og-image.jpg'],
    creator: '@metamorphiclabs',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const organizationSchema = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "Metamorphic Labs",
    "legalName": "Metamorphic Labs LLC",
    "url": "https://metamorphiclabs.com",
    "logo": {
      "@type": "ImageObject",
      "url": "https://metamorphiclabs.com/images/logo.svg",
      "width": 200,
      "height": 60
    },
    "description": "Pioneering advancements in artificial intelligence and intelligent automation",
    "foundingDate": "2024",
    "founders": [
      {
        "@type": "Person",
        "name": "Michael Laffin",
        "jobTitle": "Co-Founder & CEO"
      },
      {
        "@type": "Person",
        "name": "Rick Mellenberger",
        "jobTitle": "Co-Founder & CTO"
      }
    ],
    "contactPoint": {
      "@type": "ContactPoint",
      "email": "<EMAIL>",
      "contactType": "customer service",
      "availableLanguage": "English"
    },
    "sameAs": [
      "https://linkedin.com/company/metamorphic-labs",
      "https://github.com/metamorphic-labs"
    ],
    "serviceArea": {
      "@type": "Place",
      "name": "Worldwide"
    },
    "areaServed": "Worldwide",
    "knowsAbout": [
      "Artificial Intelligence",
      "Machine Learning",
      "Intelligent Automation",
      "Custom Software Development",
      "AI Consulting"
    ]
  };

  return (
    <html lang="en" className={`${inter.variable} ${jetbrainsMono.variable}`}>
      <head>
        <link rel="icon" href="/favicon.ico" sizes="any" />
        <link rel="icon" href="/icon.svg" type="image/svg+xml" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <link rel="manifest" href="/manifest.json" />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(organizationSchema)
          }}
        />
      </head>
      <body className="antialiased">
        <PerformanceOptimizations />
        <Layout>{children}</Layout>
        <Analytics />
        <SpeedInsights />
      </body>
    </html>
  );
}
