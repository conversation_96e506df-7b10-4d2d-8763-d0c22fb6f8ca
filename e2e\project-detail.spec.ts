import { test, expect } from '@playwright/test';
import AxeBuilder from '@axe-core/playwright';

test.describe('Project Detail Page', () => {
  const validProjectSlug = 'metamorphic-ai-platform';

  test('should not have any automatically detectable accessibility issues on project detail page', async ({ page }) => {
    await page.goto(`/projects/${validProjectSlug}`);

    // Wait for the page to load
    await expect(page.locator('h1')).toBeVisible();

    const accessibilityScanResults = await new AxeBuilder({ page }).analyze();
    expect(accessibilityScanResults.violations).toEqual([]);
  });

  test('should load project details correctly', async ({ page }) => {
    await page.goto(`/projects/${validProjectSlug}`);

    // Check that main elements are visible
    await expect(page.locator('h1')).toBeVisible();
    await expect(page.locator('h1')).toContainText('Metamorphic AI Platform');

    // Check for project status badge
    await expect(page.locator('[data-testid="status-badge"], .badge')).toBeVisible();

    // Check for project summary
    await expect(page.locator('p')).toBeVisible();

    // Check for back to projects button
    await expect(page.locator('a[href="/projects"]')).toBeVisible();

    // Check for contact CTA
    await expect(page.locator('a[href="/contact"]')).toBeVisible();
  });

  test('should navigate back to projects page', async ({ page }) => {
    await page.goto(`/projects/${validProjectSlug}`);

    // Click back to projects button
    await page.click('a[href="/projects"]');

    // Should be on projects page
    await expect(page).toHaveURL('/projects');
    await expect(page.locator('h1')).toContainText('Our Projects');
  });

  test('should navigate to contact page from project detail', async ({ page }) => {
    await page.goto(`/projects/${validProjectSlug}`);

    // Click contact CTA
    await page.click('a[href="/contact"]');

    // Should be on contact page
    await expect(page).toHaveURL('/contact');
  });
});