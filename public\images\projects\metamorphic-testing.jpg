<svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad6" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#161849;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1ae0c4;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="400" height="300" fill="url(#grad6)"/>
  <polygon points="200,70 170,120 200,170 230,120" fill="rgba(255,255,255,0.2)" stroke="rgba(255,255,255,0.4)" stroke-width="2"/>
  <circle cx="150" cy="180" r="15" fill="rgba(255,255,255,0.15)" stroke="rgba(255,255,255,0.3)" stroke-width="1"/>
  <circle cx="250" cy="180" r="15" fill="rgba(255,255,255,0.15)" stroke="rgba(255,255,255,0.3)" stroke-width="1"/>
  <circle cx="200" cy="220" r="15" fill="rgba(255,255,255,0.15)" stroke="rgba(255,255,255,0.3)" stroke-width="1"/>
  <line x1="200" y1="170" x2="150" y2="180" stroke="rgba(255,255,255,0.3)" stroke-width="1"/>
  <line x1="200" y1="170" x2="250" y2="180" stroke="rgba(255,255,255,0.3)" stroke-width="1"/>
  <line x1="200" y1="170" x2="200" y2="220" stroke="rgba(255,255,255,0.3)" stroke-width="1"/>
  <text x="200" y="280" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="18" font-weight="bold">Testing Framework</text>
</svg>
