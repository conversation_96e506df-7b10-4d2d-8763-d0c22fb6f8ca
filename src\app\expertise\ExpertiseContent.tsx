'use client';

import { motion } from 'framer-motion';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs";
import { useState } from 'react';

interface Feature {
  title: string;
  description: string;
  icon: string;
  technologies: string[];
}

interface CaseStudy {
  title: string;
  description: string;
  results: string[];
}

interface ExpertiseArea {
  title: string;
  subtitle: string;
  description: string;
  features: Feature[];
  caseStudy: CaseStudy;
}

interface ExpertiseContentProps {
  expertiseData: Record<string, ExpertiseArea>;
}

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

const featureVariants = {
  hidden: { opacity: 0, scale: 0.95 },
  visible: { opacity: 1, scale: 1 },
};

export default function ExpertiseContent({ expertiseData }: ExpertiseContentProps) {
  const [activeTab, setActiveTab] = useState("ai-platforms");

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="min-h-screen py-16 px-6 lg:px-24"
    >
      <div className="max-w-7xl mx-auto">
        {/* Hero Section */}
        <motion.div
          variants={itemVariants}
          className="text-center mb-16"
        >
          <h1 className="text-5xl lg:text-6xl font-bold mb-6 bg-gradient-to-r from-primary via-secondary to-tertiary bg-clip-text text-transparent">
            Our Expertise
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Transforming businesses through cutting-edge AI platforms, intelligent automation, 
            and custom software solutions tailored to your unique needs.
          </p>
        </motion.div>

        {/* Tabs Section */}
        <motion.div
          variants={itemVariants}
          className="w-full"
        >
          <Tabs 
            value={activeTab} 
            onValueChange={setActiveTab}
            className="w-full"
          >
            <TabsList className="grid w-full grid-cols-1 md:grid-cols-3 mb-12 h-auto p-2 bg-muted/50">
              <TabsTrigger 
                value="ai-platforms" 
                className="text-sm md:text-base py-3 px-4 data-[state=active]:bg-secondary data-[state=active]:text-white"
              >
                AI Platforms
              </TabsTrigger>
              <TabsTrigger 
                value="intelligent-automation"
                className="text-sm md:text-base py-3 px-4 data-[state=active]:bg-secondary data-[state=active]:text-white"
              >
                Intelligent Automation
              </TabsTrigger>
              <TabsTrigger 
                value="custom-software"
                className="text-sm md:text-base py-3 px-4 data-[state=active]:bg-secondary data-[state=active]:text-white"
              >
                Custom Software
              </TabsTrigger>
            </TabsList>

            {Object.entries(expertiseData).map(([key, area]) => (
              <TabsContent key={key} value={key} className="mt-0">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5 }}
                  className="space-y-12"
                >
                  {/* Area Header */}
                  <div className="text-center">
                    <h2 className="text-4xl font-bold mb-4">{area.title}</h2>
                    <h3 className="text-xl text-secondary font-semibold mb-6">{area.subtitle}</h3>
                    <p className="text-lg text-muted-foreground max-w-4xl mx-auto leading-relaxed">
                      {area.description}
                    </p>
                  </div>

                  {/* Features Grid */}
                  <motion.div
                    variants={containerVariants}
                    className="grid md:grid-cols-2 gap-8"
                  >
                    {area.features.map((feature, index) => (
                      <motion.div
                        key={index}
                        variants={featureVariants}
                        className="bg-card rounded-xl p-6 border border-border shadow-lg hover:shadow-xl transition-shadow duration-300"
                      >
                        <div className="flex items-start gap-4 mb-4">
                          <span className="text-3xl">{feature.icon}</span>
                          <div>
                            <h4 className="text-xl font-semibold mb-2">{feature.title}</h4>
                            <p className="text-muted-foreground leading-relaxed">
                              {feature.description}
                            </p>
                          </div>
                        </div>
                        
                        <div className="mt-4">
                          <h5 className="font-semibold mb-2 text-sm uppercase tracking-wide">Technologies</h5>
                          <div className="flex flex-wrap gap-2">
                            {feature.technologies.map((tech, techIndex) => (
                              <span
                                key={techIndex}
                                className="px-2 py-1 bg-tertiary/10 text-tertiary text-xs font-medium rounded-full"
                              >
                                {tech}
                              </span>
                            ))}
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </motion.div>

                  {/* Case Study */}
                  <motion.div
                    variants={itemVariants}
                    className="bg-gradient-to-br from-primary/5 via-secondary/5 to-tertiary/5 rounded-2xl p-8 border border-secondary/10"
                  >
                    <div className="text-center mb-8">
                      <h3 className="text-2xl font-bold mb-4">Featured Case Study</h3>
                      <h4 className="text-xl text-primary font-semibold mb-4">{area.caseStudy.title}</h4>
                      <p className="text-muted-foreground leading-relaxed max-w-3xl mx-auto">
                        {area.caseStudy.description}
                      </p>
                    </div>
                    
                    <div className="grid md:grid-cols-3 gap-6">
                      {area.caseStudy.results.map((result, index) => (
                        <div key={index} className="text-center">
                          <div className="bg-card rounded-lg p-4 border border-border">
                            <p className="font-semibold text-primary">{result}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </motion.div>
                </motion.div>
              </TabsContent>
            ))}
          </Tabs>
        </motion.div>

        {/* Call to Action */}
        <motion.div
          variants={itemVariants}
          className="text-center mt-20"
        >
          <h3 className="text-3xl font-bold mb-6">Ready to Transform Your Business?</h3>
          <p className="text-lg text-muted-foreground mb-8 max-w-2xl mx-auto">
            Let&apos;s discuss how our expertise can help you achieve your goals with cutting-edge AI and automation solutions.
          </p>
          <motion.a
            href="/contact"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="inline-block px-8 py-4 bg-accent text-surface font-semibold rounded-lg shadow-lg hover:bg-accent/90 transition-colors duration-200"
          >
            Start Your Project
          </motion.a>
        </motion.div>
      </div>
    </motion.div>
  );
}
