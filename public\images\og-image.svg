<svg width="1200" height="630" viewBox="0 0 1200 630" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="1200" height="630" fill="url(#gradient)"/>
  
  <!-- Gradient Definition -->
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e215d;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#00d8b4;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Logo/Icon -->
  <g transform="translate(100, 150)">
    <path d="M0 60L40 40L80 60V120L40 140L0 120V60Z" stroke="#ffffff" stroke-width="3" fill="none"/>
    <circle cx="40" cy="90" r="8" fill="#ffffff"/>
  </g>
  
  <!-- Main Text -->
  <text x="220" y="220" font-family="Arial, sans-serif" font-size="64" font-weight="bold" fill="#ffffff">
    Metamorphic Labs
  </text>
  
  <!-- Subtitle -->
  <text x="220" y="280" font-family="Arial, sans-serif" font-size="32" fill="#ffffff" opacity="0.9">
    Engineering Tomorrow's Intelligence
  </text>
  
  <!-- Description -->
  <text x="220" y="340" font-family="Arial, sans-serif" font-size="24" fill="#ffffff" opacity="0.8">
    Pioneering advancements in artificial intelligence
  </text>
  <text x="220" y="380" font-family="Arial, sans-serif" font-size="24" fill="#ffffff" opacity="0.8">
    and intelligent automation
  </text>
  
  <!-- Decorative Elements -->
  <circle cx="950" cy="150" r="60" fill="#ffffff" opacity="0.1"/>
  <circle cx="1050" cy="250" r="40" fill="#ffffff" opacity="0.1"/>
  <circle cx="900" cy="350" r="30" fill="#ffffff" opacity="0.1"/>
  
  <!-- URL -->
  <text x="220" y="520" font-family="Arial, sans-serif" font-size="20" fill="#ffffff" opacity="0.7">
    metamorphiclabs.com
  </text>
</svg>
