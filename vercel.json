{"buildCommand": "npm run build", "outputDirectory": ".next", "framework": "nextjs", "installCommand": "npm ci", "devCommand": "npm run dev", "functions": {"src/app/api/**/*.ts": {"runtime": "nodejs20.x"}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}]}], "redirects": [{"source": "/home", "destination": "/", "permanent": true}], "routes": [{"src": "/api/health", "status": 200}]}