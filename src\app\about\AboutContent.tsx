'use client';

import { motion } from 'framer-motion';

interface Founder {
  name: string;
  title: string;
  bio: string;
  expertise: string[];
  avatar: string;
}

interface TimelineEvent {
  year: string;
  title: string;
  description: string;
  icon: string;
  achievements: string[];
}

interface AboutContentProps {
  founders: Founder[];
  timelineEvents: TimelineEvent[];
}

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

const timelineItemVariants = {
  hidden: { opacity: 0, x: -50 },
  visible: { opacity: 1, x: 0 },
};

const missionVariants = {
  hidden: { opacity: 0, scale: 0.95 },
  visible: { opacity: 1, scale: 1 },
};

export default function AboutContent({ founders, timelineEvents }: AboutContentProps) {
  return (
    <>
      {/* JSON-LD Schema for founders */}
      {founders.map((founder, index) => (
        <script
          key={index}
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "Person",
              "name": founder.name,
              "jobTitle": founder.title,
              "url": "https://metamorphiclabs.com/about",
              "worksFor": {
                "@type": "Organization",
                "name": "Metamorphic Labs"
              }
            })
          }}
        />
      ))}
      
      <motion.main
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="min-h-screen py-16 px-6 lg:px-24"
      >
        {/* Hero Section */}
        <div className="max-w-6xl mx-auto">
          <motion.div
            variants={itemVariants}
            className="text-center mb-20"
          >
            <h1 className="text-5xl lg:text-6xl font-bold mb-6 bg-gradient-to-r from-primary via-secondary to-tertiary bg-clip-text text-transparent">
              About Metamorphic Labs
            </h1>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              Pioneering the future of artificial intelligence integration
            </p>
          </motion.div>

          {/* Mission Statement */}
          <motion.section
            variants={missionVariants}
            className="mb-24"
          >
            <div className="bg-gradient-to-br from-primary/5 via-secondary/5 to-tertiary/5 rounded-2xl p-8 lg:p-12 border border-secondary/10">
              <h2 className="text-3xl lg:text-4xl font-bold mb-6 text-center">Our Mission</h2>
              <div className="prose prose-lg max-w-none text-center">
                <p className="text-lg lg:text-xl leading-relaxed mb-6">
                  At Metamorphic Labs, we believe that artificial intelligence should be accessible, 
                  practical, and transformative. Our mission is to bridge the gap between cutting-edge 
                  AI research and real-world business applications.
                </p>
                <p className="text-lg leading-relaxed mb-6">
                  We specialize in creating intelligent automation solutions that don&apos;t just automate 
                  tasks—they transform entire workflows, optimize decision-making processes, and unlock 
                  new possibilities for growth and innovation.
                </p>
                <p className="text-lg leading-relaxed">
                  Through our flagship AI platform, multi-model integration systems, and custom software 
                  solutions, we empower organizations to harness the full potential of artificial intelligence 
                  while maintaining security, compliance, and human oversight.
                </p>
              </div>
            </div>
          </motion.section>

          {/* Founders Section */}
          <motion.section
            variants={containerVariants}
            className="mb-24"
          >
            <motion.h2 
              variants={itemVariants}
              className="text-4xl font-bold mb-16 text-center"
            >
              Meet Our Founders
            </motion.h2>
            <div className="grid md:grid-cols-2 gap-12 lg:gap-16">
              {founders.map((founder, index) => (
                <motion.div
                  key={index}
                  variants={itemVariants}
                  className="bg-card rounded-2xl p-8 border border-border shadow-lg hover:shadow-xl transition-shadow duration-300"
                >
                  <div className="flex flex-col items-center text-center">
                    <div className="w-32 h-32 rounded-full overflow-hidden mb-6 border-4 border-primary/20">
                      <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-primary via-secondary to-tertiary text-white text-3xl font-bold">
                        {founder.name.split(' ')[0][0]}{founder.name.split(' ')[1][0]}
                      </div>
                    </div>
                    <h3 className="text-2xl font-bold mb-2">{founder.name}</h3>
                    <p className="text-lg text-secondary font-semibold mb-4">{founder.title}</p>
                    <p className="text-muted-foreground mb-6 leading-relaxed">{founder.bio}</p>
                    
                    <div className="w-full">
                      <h4 className="font-semibold mb-3">Expertise</h4>
                      <div className="flex flex-wrap gap-2 justify-center">
                        {founder.expertise.map((skill, skillIndex) => (
                          <span
                            key={skillIndex}
                            className="px-3 py-1 bg-tertiary/10 text-tertiary text-sm font-medium rounded-full"
                          >
                            {skill}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.section>

          {/* Timeline Section */}
          <motion.section
            variants={containerVariants}
            className="mb-16"
          >
            <motion.h2 
              variants={itemVariants}
              className="text-4xl font-bold mb-16 text-center"
            >
              Our Journey
            </motion.h2>
            <div className="relative">
              {/* Timeline line */}
              <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-gradient-to-b from-primary via-secondary to-tertiary hidden lg:block"></div>
              
              <div className="space-y-12">
                {timelineEvents.map((event, index) => (
                  <motion.div
                    key={index}
                    variants={timelineItemVariants}
                    className="relative lg:pl-20"
                  >
                    {/* Timeline dot */}
                    <div className="absolute left-6 w-4 h-4 bg-primary rounded-full border-4 border-background hidden lg:block"></div>
                    
                    <div className="bg-card rounded-xl p-6 lg:p-8 border border-border shadow-lg hover:shadow-xl transition-shadow duration-300">
                      <div className="flex items-start gap-4 mb-4">
                        <span className="text-3xl">{event.icon}</span>
                        <div>
                          <h3 className="text-2xl font-bold text-primary">{event.year}</h3>
                          <h4 className="text-xl font-semibold mb-3">{event.title}</h4>
                        </div>
                      </div>
                      <p className="text-muted-foreground mb-4 leading-relaxed">{event.description}</p>
                      
                      <div>
                        <h5 className="font-semibold mb-2">Key Achievements:</h5>
                        <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
                          {event.achievements.map((achievement, achievementIndex) => (
                            <li key={achievementIndex}>{achievement}</li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </motion.section>
        </div>
      </motion.main>
    </>
  );
}
