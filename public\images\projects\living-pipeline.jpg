<svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad4" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e215d;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#26e0c4;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="400" height="300" fill="url(#grad4)"/>
  <circle cx="80" cy="150" r="25" fill="rgba(255,255,255,0.2)" stroke="rgba(255,255,255,0.4)" stroke-width="2"/>
  <circle cx="200" cy="150" r="25" fill="rgba(255,255,255,0.2)" stroke="rgba(255,255,255,0.4)" stroke-width="2"/>
  <circle cx="320" cy="150" r="25" fill="rgba(255,255,255,0.2)" stroke="rgba(255,255,255,0.4)" stroke-width="2"/>
  <line x1="105" y1="150" x2="175" y2="150" stroke="rgba(255,255,255,0.5)" stroke-width="3" marker-end="url(#arrowhead)"/>
  <line x1="225" y1="150" x2="295" y2="150" stroke="rgba(255,255,255,0.5)" stroke-width="3" marker-end="url(#arrowhead)"/>
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="rgba(255,255,255,0.5)" />
    </marker>
  </defs>
  <text x="200" y="280" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="18" font-weight="bold">Living Pipeline</text>
</svg>
