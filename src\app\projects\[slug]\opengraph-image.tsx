import { ImageResponse } from 'next/og';
import { notFound } from 'next/navigation';
import { supabase } from '../../../lib/supabaseClient';

export async function generateImageMetadata({ params }: { params: Promise<{ slug: string }> }) {
  const { slug } = await params;
  return [
    {
      id: slug,
      size: { width: 1200, height: 630 },
      alt: slug,
      contentType: 'image/png'
    }
  ];
}

export default async function Image({ params }: { params: Promise<{ id: string }> }) {
  const { id } = await params;
  const { data: project } = await supabase
    .from('projects')
    .select()
    .eq('slug', id)
    .single();

  if (!project) return notFound();

  return new ImageResponse(
    (
      <div style={{ background: '#1e215d', color: '#00d8b4', display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', width: '100%', height: '100%', padding: '50px', textAlign: 'center' }}>
        <h1 style={{ fontSize: '72px', margin: '0' }}>{project.title}</h1>
        <p style={{ fontSize: '36px', marginTop: '20px' }}>Metrics: {JSON.stringify(project.metrics_json)}</p>
      </div>
    ),
    { width: 1200, height: 630 }
  );
}