'use server'
import { supabase } from '../../lib/supabaseClient'

export async function submitContactForm(prevState: { success: boolean; message: string; } | null, formData: FormData) {
  try {
    // Extract form data
    const contactData = {
      name: formData.get('name') as string,
      email: formData.get('email') as string,
      company: formData.get('company') as string || null,
      project_type: formData.get('projectType') as string,
      budget: formData.get('budget') as string || null,
      timeline: formData.get('timeline') as string || null,
      message: formData.get('message') as string,
      nda_agreed: formData.get('ndaAccepted') === 'true'
    };

    // Validate required fields
    if (!contactData.name || !contactData.email || !contactData.message || !contactData.nda_agreed) {
      return { success: false, message: 'Please fill in all required fields and accept the NDA.' };
    }

    // Insert into Supabase
    const { error } = await supabase.from('contact_messages').insert(contactData);

    if (error) {
      console.error('Supabase error:', error);
      return { success: false, message: 'Failed to submit your message. Please try again.' };
    }

    // Placeholder for Slack webhook notification
    console.log('New contact form submission:', {
      name: contactData.name,
      email: contactData.email,
      company: contactData.company,
      projectType: contactData.project_type
    });

    return {
      success: true,
      message: 'Thank you for your message! We\'ll get back to you within 24 hours.'
    };
  } catch (error) {
    console.error('Unexpected error:', error);
    return { success: false, message: 'An unexpected error occurred. Please try again.' };
  }
}