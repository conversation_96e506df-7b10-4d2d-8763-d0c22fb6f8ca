import { render, fireEvent } from '@testing-library/react';
import { test, expect, vi } from 'vitest';
import '@testing-library/jest-dom';
import { ThemeToggle } from '../ThemeToggle';
import { useTheme } from 'next-themes';

// Mock the next-themes module
vi.mock('next-themes', () => ({
  useTheme: vi.fn(),
}));

test('ThemeToggle toggles theme correctly', () => {
  const setTheme = vi.fn();
  const mockUseTheme = useTheme as vi.Mock;
  mockUseTheme.mockReturnValue({ theme: 'light', setTheme });

  const { getByRole } = render(<ThemeToggle />);
  const button = getByRole('button', { name: /toggle theme/i });

  // Initial state: light theme
  expect(button).toHaveAttribute('title', 'Toggle theme (current: light)');

  // Click to switch to dark theme
  fireEvent.click(button);
  expect(setTheme).toHaveBeenCalledWith('dark');

  // Simulate theme change to dark
  mockUseTheme.mockReturnValue({ theme: 'dark', setTheme });
  render(<ThemeToggle />, { container: document.body }); // Re-render to reflect theme change
  expect(getByRole('button', { name: /toggle theme/i })).toHaveAttribute('title', 'Toggle theme (current: dark)');

  // Click to switch back to light theme
  fireEvent.click(button);
  expect(setTheme).toHaveBeenCalledWith('light');
});