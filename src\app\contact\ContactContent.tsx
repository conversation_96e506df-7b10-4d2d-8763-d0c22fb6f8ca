'use client';

import { motion, AnimatePresence } from 'framer-motion';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useFormState } from 'react-dom';
import { submitContactForm } from './actions';
import { toast } from "sonner";
import { Toaster } from "@/components/ui/sonner";
import { useEffect, useState } from 'react';

import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

// Define the form schema using Zod
const formSchema = z.object({
  name: z.string().min(2, {
    message: "Name must be at least 2 characters long.",
  }),
  email: z.string().email({
    message: "Please enter a valid email address.",
  }),
  company: z.string().optional(),
  projectType: z.string().min(1, {
    message: "Please select a project type.",
  }),
  budget: z.string().optional(),
  timeline: z.string().optional(),
  message: z.string().min(10, {
    message: "Message must be at least 10 characters long.",
  }),
  ndaAccepted: z.boolean().refine((val) => val === true, {
    message: "You must accept the NDA to proceed.",
  }),
});

const projectTypes = [
  { value: "ai-platform", label: "AI Platform Development" },
  { value: "automation", label: "Intelligent Automation" },
  { value: "custom-software", label: "Custom Software Development" },
  { value: "consulting", label: "AI Consulting & Strategy" },
  { value: "integration", label: "AI Integration Services" },
  { value: "other", label: "Other" },
];

const budgetRanges = [
  { value: "10k-25k", label: "$10K - $25K" },
  { value: "25k-50k", label: "$25K - $50K" },
  { value: "50k-100k", label: "$50K - $100K" },
  { value: "100k-250k", label: "$100K - $250K" },
  { value: "250k+", label: "$250K+" },
  { value: "discuss", label: "Let's Discuss" },
];

const timelineOptions = [
  { value: "asap", label: "ASAP" },
  { value: "1-3months", label: "1-3 Months" },
  { value: "3-6months", label: "3-6 Months" },
  { value: "6-12months", label: "6-12 Months" },
  { value: "12months+", label: "12+ Months" },
  { value: "flexible", label: "Flexible" },
];

export default function ContactContent() {
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      email: "",
      company: "",
      projectType: "",
      budget: "",
      timeline: "",
      message: "",
      ndaAccepted: false,
    },
  });

  const [state, formAction] = useFormState(submitContactForm, { success: false, message: '' });

  useEffect(() => {
    if (state?.message) {
      setIsSubmitting(false);
      toast(state.message, {
        description: state.success ? "Your message has been sent!" : "There was an error sending your message.",
        action: {
          label: "Close",
          onClick: () => toast.dismiss(),
        },
      });
      
      if (state.success) {
        form.reset();
        setCurrentStep(1);
      }
    }
  }, [state, form]);

  const nextStep = async () => {
    const fields = currentStep === 1
      ? ['name', 'email', 'company'] as const
      : ['projectType', 'budget', 'timeline'] as const;

    const isValid = await form.trigger(fields);
    if (isValid) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    setCurrentStep(currentStep - 1);
  };

  const onSubmit = async (data: z.infer<typeof formSchema>) => {
    setIsSubmitting(true);
    const formData = new FormData();
    Object.entries(data).forEach(([key, value]) => {
      formData.append(key, value.toString());
    });
    formAction(formData);
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
  };

  const stepVariants = {
    hidden: { opacity: 0, x: 50 },
    visible: { opacity: 1, x: 0 },
    exit: { opacity: 0, x: -50 },
  };

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="min-h-screen py-16 px-6 lg:px-24"
    >
      <div className="max-w-6xl mx-auto">
        {/* Hero Section */}
        <motion.div
          variants={itemVariants}
          className="text-center mb-16"
        >
          <h1 className="text-5xl lg:text-6xl font-bold mb-6 bg-gradient-to-r from-primary via-secondary to-tertiary bg-clip-text text-transparent">
            Let&apos;s Build Something Amazing
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Ready to transform your business with AI? Get in touch with our experts 
            and let&apos;s discuss how we can help you achieve your goals.
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-12">
          {/* Contact Form */}
          <motion.div
            variants={itemVariants}
            className="order-2 lg:order-1"
          >
            <Card className="shadow-xl border-0 bg-card/50 backdrop-blur">
              <CardHeader>
                <CardTitle className="text-2xl">Contact Form</CardTitle>
                <CardDescription>
                  Tell us about your project and we&apos;ll get back to you within 24 hours.
                </CardDescription>
                
                {/* Progress Indicator */}
                <div className="flex items-center space-x-2 mt-4">
                  {[1, 2, 3].map((step) => (
                    <div key={step} className="flex items-center">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                        step <= currentStep
                          ? 'bg-secondary text-white'
                          : 'bg-muted text-muted-foreground'
                      }`}>
                        {step}
                      </div>
                      {step < 3 && (
                        <div className={`w-12 h-0.5 ${
                          step < currentStep ? 'bg-secondary' : 'bg-muted'
                        }`} />
                      )}
                    </div>
                  ))}
                </div>
                <div className="flex justify-between text-xs text-muted-foreground mt-2">
                  <span>Contact Info</span>
                  <span>Project Details</span>
                  <span>Message & NDA</span>
                </div>
              </CardHeader>
              
              <CardContent>
                <Form {...form}>
                  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                    <AnimatePresence mode="wait">
                      {currentStep === 1 && (
                        <motion.div
                          key="step1"
                          variants={stepVariants}
                          initial="hidden"
                          animate="visible"
                          exit="exit"
                          className="space-y-4"
                        >
                          <FormField
                            control={form.control}
                            name="name"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Full Name *</FormLabel>
                                <FormControl>
                                  <Input placeholder="John Doe" {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="email"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Email Address *</FormLabel>
                                <FormControl>
                                  <Input placeholder="<EMAIL>" {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="company"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Company</FormLabel>
                                <FormControl>
                                  <Input placeholder="Your Company" {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <Button type="button" onClick={nextStep} className="w-full">
                            Next Step
                          </Button>
                        </motion.div>
                      )}

                      {currentStep === 2 && (
                        <motion.div
                          key="step2"
                          variants={stepVariants}
                          initial="hidden"
                          animate="visible"
                          exit="exit"
                          className="space-y-4"
                        >
                          <FormField
                            control={form.control}
                            name="projectType"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Project Type *</FormLabel>
                                <Select onValueChange={field.onChange} defaultValue={field.value}>
                                  <FormControl>
                                    <SelectTrigger>
                                      <SelectValue placeholder="Select project type" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    {projectTypes.map((type) => (
                                      <SelectItem key={type.value} value={type.value}>
                                        {type.label}
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="budget"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Budget Range</FormLabel>
                                <Select onValueChange={field.onChange} defaultValue={field.value}>
                                  <FormControl>
                                    <SelectTrigger>
                                      <SelectValue placeholder="Select budget range" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    {budgetRanges.map((budget) => (
                                      <SelectItem key={budget.value} value={budget.value}>
                                        {budget.label}
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="timeline"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Timeline</FormLabel>
                                <Select onValueChange={field.onChange} defaultValue={field.value}>
                                  <FormControl>
                                    <SelectTrigger>
                                      <SelectValue placeholder="Select timeline" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    {timelineOptions.map((timeline) => (
                                      <SelectItem key={timeline.value} value={timeline.value}>
                                        {timeline.label}
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <div className="flex space-x-4">
                            <Button type="button" variant="outline" onClick={prevStep} className="flex-1">
                              Previous
                            </Button>
                            <Button type="button" onClick={nextStep} className="flex-1">
                              Next Step
                            </Button>
                          </div>
                        </motion.div>
                      )}

                      {currentStep === 3 && (
                        <motion.div
                          key="step3"
                          variants={stepVariants}
                          initial="hidden"
                          animate="visible"
                          exit="exit"
                          className="space-y-4"
                        >
                          <FormField
                            control={form.control}
                            name="message"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Project Description *</FormLabel>
                                <FormControl>
                                  <Textarea
                                    placeholder="Tell us about your project, goals, and any specific requirements..."
                                    className="min-h-[120px]"
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="ndaAccepted"
                            render={({ field }) => (
                              <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4 bg-accent/10">
                                <FormControl>
                                  <Checkbox
                                    checked={field.value}
                                    onCheckedChange={field.onChange}
                                  />
                                </FormControl>
                                <div className="space-y-1 leading-none">
                                  <FormLabel>
                                    I agree to the Non-Disclosure Agreement *
                                  </FormLabel>
                                  <FormDescription>
                                    We take confidentiality seriously. All project discussions are protected under NDA.
                                  </FormDescription>
                                </div>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <div className="flex space-x-4">
                            <Button type="button" variant="outline" onClick={prevStep} className="flex-1">
                              Previous
                            </Button>
                            <Button
                              type="submit"
                              className="flex-1"
                              disabled={isSubmitting}
                            >
                              {isSubmitting ? "Sending..." : "Send Message"}
                            </Button>
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </form>
                </Form>
              </CardContent>
            </Card>
          </motion.div>

          {/* Contact Information & Calendly */}
          <motion.div
            variants={itemVariants}
            className="order-1 lg:order-2 space-y-8"
          >
            {/* Contact Info */}
            <Card>
              <CardHeader>
                <CardTitle>Get In Touch</CardTitle>
                <CardDescription>
                  Multiple ways to reach our team
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-secondary/10 rounded-lg flex items-center justify-center">
                    <span className="text-secondary">📧</span>
                  </div>
                  <div>
                    <p className="font-medium">Email</p>
                    <p className="text-sm text-muted-foreground"><EMAIL></p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-secondary/10 rounded-lg flex items-center justify-center">
                    <span className="text-secondary">⏱️</span>
                  </div>
                  <div>
                    <p className="font-medium">Response Time</p>
                    <p className="text-sm text-muted-foreground">Within 24 hours</p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-secondary/10 rounded-lg flex items-center justify-center">
                    <span className="text-secondary">🌍</span>
                  </div>
                  <div>
                    <p className="font-medium">Timezone</p>
                    <p className="text-sm text-muted-foreground">EST (UTC-5)</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Calendly Placeholder */}
            <Card>
              <CardHeader>
                <CardTitle>Schedule a Call</CardTitle>
                <CardDescription>
                  Book a free 30-minute consultation
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="w-full h-64 bg-gradient-to-br from-primary/5 via-secondary/5 to-tertiary/5 rounded-lg flex flex-col items-center justify-center border-2 border-dashed border-secondary/20">
                  <div className="text-4xl mb-4">📅</div>
                  <p className="text-center text-muted-foreground mb-4">
                    Calendly integration will be embedded here
                  </p>
                  <Badge variant="secondary">Coming Soon</Badge>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>

      <Toaster />
    </motion.div>
  );
}
