<svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2a2d73;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1ae0c4;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="400" height="300" fill="url(#grad2)"/>
  <polygon points="200,50 150,100 200,150 250,100" fill="rgba(255,255,255,0.2)" stroke="rgba(255,255,255,0.4)" stroke-width="2"/>
  <polygon points="200,150 150,200 200,250 250,200" fill="rgba(255,255,255,0.1)" stroke="rgba(255,255,255,0.3)" stroke-width="2"/>
  <line x1="200" y1="100" x2="200" y2="200" stroke="rgba(255,255,255,0.5)" stroke-width="3"/>
  <text x="200" y="280" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="18" font-weight="bold">AI Broker</text>
</svg>
