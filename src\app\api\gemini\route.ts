import { GoogleGenerative<PERSON><PERSON> } from '@google/generative-ai';
import { NextRequest, NextResponse } from 'next/server';

const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY || '');

const SYSTEM_PROMPT = `You are an AI assistant for Metamorphic Labs LLC, a cutting-edge technology company specializing in artificial intelligence and intelligent automation. Here's key information about the company:

**Company Overview:**
- Metamorphic Labs LLC is pioneering advancements in AI and intelligent automation
- Founded by <PERSON> (Co-Founder & CEO) and <PERSON> (Co-Founder & CTO)
- Focus on adaptive multi-model AI toolchains and intelligent automation solutions

**Key Projects:**
1. **Metamorphic AI Platform** - Adaptive multi-model toolchain (Concept/Design Phase)
2. **AI Integration & Broker System** - Smart LLM router (Phase 2 Complete)
3. **Metamorphic SaaS Suite** - On-demand SaaS factory with EFX revenue share (Concept)
4. **Living Pipeline** - Self-optimizing CI/CD system (Concept)
5. **Metamorphic Reactor** - VS Code extension for AI-assisted development (Beta v0.9.0)
6. **Metamorphic Testing** - Metamorphic validation framework (Concept)

**Expertise Areas:**
- AI Platforms & Multi-model integration
- Intelligent Automation & Process optimization
- Custom Software Development
- Enterprise AI solutions

**Company Values:**
- Innovation through adaptive technology
- Seamless integration of AI into existing workflows
- Enterprise-grade security and reliability
- Cutting-edge research and development

Please respond helpfully about Metamorphic Labs, our projects, capabilities, and how we can help with AI and automation solutions. Keep responses concise but informative, and always maintain a professional yet approachable tone.`;

export async function POST(request: NextRequest) {
  try {
    const { message } = await request.json();

    if (!message || typeof message !== 'string') {
      return NextResponse.json(
        { error: 'Message is required' },
        { status: 400 }
      );
    }

    if (!process.env.GEMINI_API_KEY) {
      return NextResponse.json(
        { error: 'Gemini API key not configured' },
        { status: 500 }
      );
    }

    const model = genAI.getGenerativeModel({ model: 'gemini-2.0-flash-exp' });

    const chat = model.startChat({
      history: [
        {
          role: 'user',
          parts: [{ text: SYSTEM_PROMPT }],
        },
        {
          role: 'model',
          parts: [{ text: 'I understand. I\'m ready to assist as an AI representative for Metamorphic Labs LLC, providing information about our AI and automation solutions, projects, and capabilities.' }],
        },
      ],
    });

    const result = await chat.sendMessage(message);
    const response = await result.response;
    const text = response.text();

    return NextResponse.json({ response: text });
  } catch (error) {
    console.error('Gemini API error:', error);
    return NextResponse.json(
      { error: 'Failed to process request' },
      { status: 500 }
    );
  }
}
