/** @type {import('next-sitemap').IConfig} */
module.exports = {
  siteUrl: 'https://metamorphiclabs.com',
  generateRobotsTxt: true,
  generateIndexSitemap: false,
  changefreq: 'weekly',
  priority: 0.7,
  sitemapSize: 5000,
  exclude: [
    '/api/*',
    '/admin/*',
    '/private/*'
  ],
  transform: async (config, path) => {
    // Custom priority and changefreq for different page types
    let priority = 0.7;
    let changefreq = 'weekly';

    if (path === '/') {
      priority = 1.0;
      changefreq = 'daily';
    } else if (path.startsWith('/projects/')) {
      priority = 0.8;
      changefreq = 'monthly';
    } else if (['/about', '/expertise', '/contact'].includes(path)) {
      priority = 0.9;
      changefreq = 'monthly';
    } else if (path.startsWith('/legal/')) {
      priority = 0.3;
      changefreq = 'yearly';
    }

    return {
      loc: path,
      changefreq,
      priority,
      lastmod: config.autoLastmod ? new Date().toISOString() : undefined,
    };
  },
  robotsTxtOptions: {
    policies: [
      {
        userAgent: '*',
        allow: '/',
        disallow: ['/api/', '/admin/', '/private/']
      },
      {
        userAgent: 'GPTBot',
        disallow: ['/']
      },
      {
        userAgent: 'ChatGPT-User',
        disallow: ['/']
      },
      {
        userAgent: 'CCBot',
        disallow: ['/']
      }
    ],
    additionalSitemaps: [
      'https://metamorphiclabs.com/sitemap.xml'
    ]
  }
}